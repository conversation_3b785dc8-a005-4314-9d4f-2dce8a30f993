import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// 网络测试工具 - 用于测试公告服务器连接
void main() async {
  print('🧪 开始测试公告服务器连接...\n');
  
  // 测试URL
  final urls = [
    'https://www.dznovel.top/announcement.json',
    'http://47.120.19.139:8000/announcement.json',
  ];
  
  for (int i = 0; i < urls.length; i++) {
    final url = urls[i];
    print('🔗 测试URL ${i + 1}: $url');
    
    try {
      final headers = {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
        'User-Agent': 'DaizongNovelApp/1.0',
      };
      
      print('📡 发送请求...');
      final response = await http.get(
        Uri.parse(url), 
        headers: headers
      ).timeout(Duration(seconds: 10));
      
      print('📊 响应状态码: ${response.statusCode}');
      print('📏 响应长度: ${response.body.length} 字符');
      print('🔍 响应头: ${response.headers}');
      
      if (response.statusCode == 200) {
        print('✅ 请求成功！');
        
        if (response.body.isNotEmpty) {
          print('📄 响应内容:');
          print('─' * 50);
          print(response.body);
          print('─' * 50);
          
          try {
            final data = json.decode(response.body);
            print('✅ JSON解析成功！');
            print('🔍 解析后的数据:');
            print('  - ID: ${data['id']}');
            print('  - 标题: ${data['title']}');
            print('  - 是否激活: ${data['is_active']}');
            print('  - 是否重要: ${data['is_important']}');
            print('  - 创建时间: ${data['created_at']}');
            
            if (data['is_active'] == true) {
              print('✅ 公告已激活，应该会显示');
            } else {
              print('⚠️ 公告未激活，不会显示');
            }
          } catch (e) {
            print('❌ JSON解析失败: $e');
          }
        } else {
          print('⚠️ 响应内容为空');
        }
      } else {
        print('❌ 请求失败，状态码: ${response.statusCode}');
        print('📄 错误内容: ${response.body}');
      }
      
    } catch (e) {
      print('❌ 网络请求异常: $e');
      
      if (e is SocketException) {
        print('🌐 网络连接问题，可能的原因:');
        print('  - 服务器不可达');
        print('  - DNS解析失败');
        print('  - 防火墙阻止');
      } else if (e.toString().contains('timeout')) {
        print('⏰ 请求超时，可能的原因:');
        print('  - 服务器响应慢');
        print('  - 网络延迟高');
      }
    }
    
    print('\n' + '=' * 60 + '\n');
  }
  
  print('🏁 测试完成！');
  print('\n💡 如果所有URL都失败，请检查:');
  print('1. 网络连接是否正常');
  print('2. 服务器是否运行');
  print('3. announcement.json文件是否存在');
  print('4. 文件权限是否正确');
  print('5. 防火墙设置');
}

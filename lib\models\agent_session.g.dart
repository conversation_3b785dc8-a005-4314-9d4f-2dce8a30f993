// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_session.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AgentSessionAdapter extends TypeAdapter<AgentSession> {
  @override
  final int typeId = 15;

  @override
  AgentSession read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AgentSession(
      id: fields[0] as String,
      title: fields[1] as String,
      createdAt: fields[2] as DateTime,
      lastUpdatedAt: fields[3] as DateTime,
      type: fields[4] as AgentSessionType,
      novelTitle: fields[5] as String,
      summary: fields[6] as String?,
      chapterNumber: fields[7] as int?,
      isActive: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, AgentSession obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.createdAt)
      ..writeByte(3)
      ..write(obj.lastUpdatedAt)
      ..writeByte(4)
      ..write(obj.type)
      ..writeByte(5)
      ..write(obj.novelTitle)
      ..writeByte(6)
      ..write(obj.summary)
      ..writeByte(7)
      ..write(obj.chapterNumber)
      ..writeByte(8)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AgentSessionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AgentSessionTypeAdapter extends TypeAdapter<AgentSessionType> {
  @override
  final int typeId = 14;

  @override
  AgentSessionType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AgentSessionType.chat;
      case 1:
        return AgentSessionType.creative;
      default:
        return AgentSessionType.chat;
    }
  }

  @override
  void write(BinaryWriter writer, AgentSessionType obj) {
    switch (obj) {
      case AgentSessionType.chat:
        writer.writeByte(0);
        break;
      case AgentSessionType.creative:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AgentSessionTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

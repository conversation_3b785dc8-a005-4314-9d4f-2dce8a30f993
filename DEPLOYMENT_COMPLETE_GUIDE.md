# 岱宗文脉 Web版完整部署指南

## 🎯 问题解决总结

我们已经成功解决了以下问题：
1. ✅ Web版SSL证书验证失败问题
2. ✅ Flutter Web编译错误问题
3. ✅ CORS跨域访问问题
4. ✅ 代理服务器部署方案

## 📋 部署步骤

### 第一步：构建Web版本

```bash
# 在项目根目录执行
flutter build web --release
```

构建完成后，Web文件将生成在 `build/web` 目录中。

### 第二步：部署CORS代理服务器

#### 方法1：一键部署（推荐）

1. **上传文件到服务器**
   ```bash
   # 在宝塔面板中，进入网站根目录
   cd /www/wwwroot/www.dznovel.top
   mkdir -p proxy
   cd proxy
   
   # 上传以下文件：
   # - web/cors_proxy_server.py
   # - web/quick_deploy.py
   # - web/test_proxy.py
   ```

2. **执行一键部署**
   ```bash
   python3 quick_deploy.py
   ```

#### 方法2：手动部署

```bash
# 启动代理服务器
python3 cors_proxy_server.py 8080

# 测试代理服务器
python3 test_proxy.py http://localhost:8080
```

### 第三步：配置Nginx（推荐）

在宝塔面板的网站配置中添加：

```nginx
location /proxy/ {
    proxy_pass http://127.0.0.1:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # CORS头部
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
    
    # 处理预检请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        add_header Access-Control-Max-Age 86400;
        add_header Content-Length 0;
        add_header Content-Type text/plain;
        return 204;
    }
}
```

### 第四步：上传Web文件

1. **上传构建文件**
   ```bash
   # 将 build/web 目录中的所有文件上传到网站根目录
   # 确保包含以下文件：
   # - index.html
   # - main.dart.js
   # - flutter.js
   # - assets/ 目录
   # - icons/ 目录
   ```

2. **设置文件权限**
   ```bash
   chmod -R 755 /www/wwwroot/www.dznovel.top
   ```

### 第五步：测试部署

1. **测试代理服务器**
   ```bash
   curl "http://localhost:8080/proxy/https://httpbin.org/get"
   ```

2. **测试Web应用**
   - 访问：`https://www.dznovel.top`
   - 检查API连接是否正常
   - 测试各项功能

## 🔧 服务管理

### 使用systemd管理代理服务器

```bash
# 启动服务
systemctl start dznovel-proxy

# 停止服务
systemctl stop dznovel-proxy

# 重启服务
systemctl restart dznovel-proxy

# 查看状态
systemctl status dznovel-proxy

# 查看日志
journalctl -u dznovel-proxy -f
```

### 手动管理

```bash
# 后台启动
nohup python3 cors_proxy_server.py 8080 > proxy.log 2>&1 &

# 查看进程
ps aux | grep cors_proxy_server

# 停止进程
pkill -f cors_proxy_server
```

## 🧪 测试验证

### 1. 代理服务器测试

```bash
# 基础测试
python3 test_proxy.py http://localhost:8080

# 完整测试
python3 test_proxy.py http://localhost:8080
# 选择选项2进行完整测试
```

### 2. Web应用测试

1. **访问应用**：`https://www.dznovel.top`
2. **测试API连接**：在设置页面测试API连接
3. **功能测试**：尝试生成小说内容

## 📁 文件结构

```
/www/wwwroot/www.dznovel.top/
├── index.html                 # Web应用主页
├── main.dart.js              # 编译后的Dart代码
├── flutter.js                # Flutter Web运行时
├── assets/                   # 资源文件
├── icons/                    # 图标文件
└── proxy/                    # 代理服务器
    ├── cors_proxy_server.py  # 代理服务器主程序
    ├── quick_deploy.py       # 一键部署脚本
    └── test_proxy.py         # 测试工具
```

## 🔍 故障排除

### 常见问题

1. **代理服务器无法启动**
   - 检查端口是否被占用：`netstat -tlnp | grep 8080`
   - 更换端口：`python3 cors_proxy_server.py 8081`

2. **SSL证书错误**
   - 确保代理服务器正在运行
   - 检查防火墙设置
   - 验证Nginx配置

3. **CORS错误**
   - 检查Nginx配置中的CORS头部
   - 清除浏览器缓存
   - 重启代理服务器

4. **Web应用无法加载**
   - 检查文件权限
   - 验证文件完整性
   - 查看浏览器控制台错误

### 日志查看

```bash
# 代理服务器日志
journalctl -u dznovel-proxy -f

# Nginx日志
tail -f /www/wwwlogs/www.dznovel.top.log

# 系统日志
tail -f /var/log/messages
```

## 🎉 部署完成

部署完成后，您的Web应用将能够：

1. ✅ 正常访问所有API服务
2. ✅ 解决SSL证书验证问题
3. ✅ 支持跨域请求
4. ✅ 稳定运行在生产环境

访问地址：`https://www.dznovel.top`

---

**注意**：如果遇到任何问题，请参考 `web/SSL_SOLUTION_GUIDE.md` 获取详细的故障排除指南。

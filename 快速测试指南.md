# 🧪 实时公告推送快速测试指南

## 🚀 测试步骤

### 1. 启动应用
```bash
flutter run
```

### 2. 观察控制台日志
启动后应该看到：
```
🔔 初始化公告服务...
🔄 启动实时公告检查定时器，每30秒检查一次
⏰ 定时检查公告更新...
```

### 3. 测试实时推送

#### 方法1：本地测试
1. 将 `test_announcement.json` 复制为 `announcement.json`
2. 等待最多30秒
3. 应该看到测试公告弹出

#### 方法2：服务器测试
1. 将 `announcement.json` 上传到服务器
2. 修改文件中的 `id` 字段
3. 保存文件
4. 等待最多30秒观察推送

### 4. 验证日志输出

成功时应该看到：
```
⏰ 定时检查公告更新...
🆕 发现新公告: [公告标题]
📢 公告对话框已显示
```

无更新时应该看到：
```
⏰ 定时检查公告更新...
📋 公告无更新
```

## 🔧 测试用例

### 测试用例1：新公告推送
```json
{
  "id": "test_001",
  "title": "测试公告1",
  "content": "这是第一个测试公告",
  "created_at": "2025-01-27T12:00:00Z",
  "is_important": false,
  "is_active": true
}
```

### 测试用例2：重要公告推送
```json
{
  "id": "test_002",
  "title": "⚠️ 重要测试公告",
  "content": "这是重要公告测试",
  "created_at": "2025-01-27T12:05:00Z",
  "is_important": true,
  "is_active": true
}
```

### 测试用例3：停用公告
```json
{
  "id": "test_003",
  "title": "停用测试",
  "content": "此公告应该不会显示",
  "created_at": "2025-01-27T12:10:00Z",
  "is_important": false,
  "is_active": false
}
```

## ✅ 预期结果

1. **应用启动**：看到初始化日志
2. **定时检查**：每30秒看到检查日志
3. **新公告推送**：修改ID后30秒内弹出公告
4. **重要公告样式**：重要公告显示不同颜色
5. **停用公告**：is_active为false的公告不显示

## 🐛 常见问题

### 问题1：公告不弹出
- 检查JSON格式
- 确认is_active为true
- 查看控制台错误信息

### 问题2：重复弹出
- 确认每次测试都修改了ID
- 清除应用缓存重新测试

### 问题3：网络错误
- 检查URL是否可访问
- 确认网络连接正常
- 查看HTTP状态码

## 📊 性能监控

监控以下指标：
- 检查频率：30秒一次
- 网络请求时间：应小于5秒
- 内存使用：定时器不应造成内存泄漏
- 电池消耗：后台检查应该很轻量

## 🎯 成功标准

测试成功的标准：
- ✅ 应用启动时初始化公告服务
- ✅ 定时器正常工作（每30秒检查）
- ✅ 新公告能在30秒内推送
- ✅ 重要公告样式正确
- ✅ 停用公告不显示
- ✅ 网络错误时不崩溃
- ✅ 控制台日志清晰可读

#!/bin/bash

# 岱宗文脉 Web版 宝塔面板部署脚本
# 用于在宝塔面板服务器上部署CORS代理服务器

echo "🎯 岱宗文脉 Web版 宝塔面板部署脚本"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

echo "✅ Python3 已安装: $(python3 --version)"

# 设置部署目录
DEPLOY_DIR="/www/wwwroot/www.dznovel.top/proxy"
WEB_ROOT="/www/wwwroot/www.dznovel.top"

echo "📁 部署目录: $DEPLOY_DIR"
echo "🌐 网站根目录: $WEB_ROOT"

# 创建部署目录
mkdir -p "$DEPLOY_DIR"

# 复制代理服务器文件
echo "📋 复制代理服务器文件..."
cp cors_proxy_server.py "$DEPLOY_DIR/"
cp start_proxy.py "$DEPLOY_DIR/"

# 设置执行权限
chmod +x "$DEPLOY_DIR/cors_proxy_server.py"
chmod +x "$DEPLOY_DIR/start_proxy.py"

# 创建systemd服务文件
echo "⚙️  创建系统服务..."
cat > /etc/systemd/system/dznovel-proxy.service << EOF
[Unit]
Description=DaiZong Novel CORS Proxy Server
After=network.target

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=$DEPLOY_DIR
ExecStart=/usr/bin/python3 $DEPLOY_DIR/cors_proxy_server.py 8080
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd配置
systemctl daemon-reload

# 启用并启动服务
systemctl enable dznovel-proxy
systemctl start dznovel-proxy

# 检查服务状态
if systemctl is-active --quiet dznovel-proxy; then
    echo "✅ CORS代理服务器已成功启动"
    echo "📡 服务状态: $(systemctl is-active dznovel-proxy)"
    echo "🌐 代理地址: http://$(hostname -I | awk '{print $1}'):8080"
    echo "🔧 使用方法: http://your-domain.com:8080/proxy/https://api.openai-proxy.org"
else
    echo "❌ CORS代理服务器启动失败"
    echo "📋 查看日志: journalctl -u dznovel-proxy -f"
    exit 1
fi

# 创建Nginx配置（可选）
echo ""
echo "🔧 可选：配置Nginx反向代理"
echo "在宝塔面板中添加以下Nginx配置："
echo ""
cat << 'EOF'
# 在网站配置中添加以下location块
location /proxy/ {
    proxy_pass http://127.0.0.1:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # CORS头部
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
    
    # 处理预检请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        add_header Access-Control-Max-Age 86400;
        add_header Content-Length 0;
        add_header Content-Type text/plain;
        return 204;
    }
}
EOF

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 管理命令："
echo "  启动服务: systemctl start dznovel-proxy"
echo "  停止服务: systemctl stop dznovel-proxy"
echo "  重启服务: systemctl restart dznovel-proxy"
echo "  查看状态: systemctl status dznovel-proxy"
echo "  查看日志: journalctl -u dznovel-proxy -f"
echo ""
echo "🌐 测试代理："
echo "  curl 'http://localhost:8080/proxy/https://httpbin.org/get'"
echo ""

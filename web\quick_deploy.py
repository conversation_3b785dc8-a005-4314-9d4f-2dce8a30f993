#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速部署脚本
用于在宝塔面板服务器上快速部署CORS代理服务器
"""

import os
import sys
import subprocess
import socket
import time
from pathlib import Path

def check_python():
    """检查Python环境"""
    try:
        result = subprocess.run([sys.executable, '--version'], 
                              capture_output=True, text=True)
        print(f"✅ Python环境: {result.stdout.strip()}")
        return True
    except Exception as e:
        print(f"❌ Python环境检查失败: {e}")
        return False

def find_free_port(start_port=8080):
    """查找可用端口"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def create_service_file(port=8080):
    """创建systemd服务文件"""
    service_content = f"""[Unit]
Description=DaiZong Novel CORS Proxy Server
After=network.target

[Service]
Type=simple
User=www
Group=www
WorkingDirectory={os.getcwd()}
ExecStart=/usr/bin/python3 {os.getcwd()}/cors_proxy_server.py {port}
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
"""
    
    try:
        with open('/etc/systemd/system/dznovel-proxy.service', 'w') as f:
            f.write(service_content)
        print("✅ 系统服务文件创建成功")
        return True
    except PermissionError:
        print("⚠️  需要root权限创建系统服务，将使用手动启动方式")
        return False
    except Exception as e:
        print(f"❌ 创建系统服务文件失败: {e}")
        return False

def start_service():
    """启动系统服务"""
    try:
        subprocess.run(['systemctl', 'daemon-reload'], check=True)
        subprocess.run(['systemctl', 'enable', 'dznovel-proxy'], check=True)
        subprocess.run(['systemctl', 'start', 'dznovel-proxy'], check=True)
        
        # 检查服务状态
        result = subprocess.run(['systemctl', 'is-active', 'dznovel-proxy'], 
                              capture_output=True, text=True)
        if result.stdout.strip() == 'active':
            print("✅ 系统服务启动成功")
            return True
        else:
            print("❌ 系统服务启动失败")
            return False
    except Exception as e:
        print(f"❌ 启动系统服务失败: {e}")
        return False

def start_manual(port=8080):
    """手动启动代理服务器"""
    try:
        print(f"🚀 手动启动代理服务器（端口: {port}）...")
        print("按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        subprocess.run([sys.executable, 'cors_proxy_server.py', str(port)])
        return True
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 手动启动失败: {e}")
        return False

def print_nginx_config(port=8080):
    """打印Nginx配置"""
    nginx_config = f"""
# 在宝塔面板网站配置中添加以下location块：

location /proxy/ {{
    proxy_pass http://127.0.0.1:{port};
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # CORS头部
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
    
    # 处理预检请求
    if ($request_method = 'OPTIONS') {{
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        add_header Access-Control-Max-Age 86400;
        add_header Content-Length 0;
        add_header Content-Type text/plain;
        return 204;
    }}
}}
"""
    print("🔧 Nginx配置:")
    print(nginx_config)

def test_proxy(port=8080):
    """测试代理服务器"""
    try:
        import urllib.request
        import urllib.error
        
        test_url = f"http://localhost:{port}/proxy/https://httpbin.org/get"
        print(f"🧪 测试代理服务器: {test_url}")
        
        with urllib.request.urlopen(test_url, timeout=10) as response:
            if response.status == 200:
                print("✅ 代理服务器测试成功")
                return True
            else:
                print(f"❌ 代理服务器测试失败: {response.status}")
                return False
    except Exception as e:
        print(f"❌ 代理服务器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 岱宗文脉 CORS代理服务器快速部署")
    print("=" * 50)
    
    # 检查必要文件
    if not Path('cors_proxy_server.py').exists():
        print("❌ 找不到 cors_proxy_server.py 文件")
        return 1
    
    # 检查Python环境
    if not check_python():
        return 1
    
    # 查找可用端口
    port = find_free_port(8080)
    if not port:
        print("❌ 无法找到可用端口")
        return 1
    
    print(f"📡 使用端口: {port}")
    
    # 尝试创建系统服务
    if os.geteuid() == 0:  # 检查是否为root用户
        print("🔧 检测到root权限，创建系统服务...")
        if create_service_file(port) and start_service():
            print("✅ 系统服务部署成功")
            print_nginx_config(port)
            
            # 等待服务启动
            time.sleep(3)
            test_proxy(port)
            
            print("\n📋 管理命令:")
            print("  systemctl start dznovel-proxy    # 启动服务")
            print("  systemctl stop dznovel-proxy     # 停止服务")
            print("  systemctl restart dznovel-proxy  # 重启服务")
            print("  systemctl status dznovel-proxy   # 查看状态")
            print("  journalctl -u dznovel-proxy -f   # 查看日志")
            return 0
    
    # 手动启动
    print("🔧 使用手动启动方式...")
    print_nginx_config(port)
    print("\n⚠️  注意：手动启动的服务在终端关闭后会停止")
    print("建议使用 screen 或 nohup 在后台运行")
    print()
    
    return 0 if start_manual(port) else 1

if __name__ == '__main__':
    sys.exit(main())

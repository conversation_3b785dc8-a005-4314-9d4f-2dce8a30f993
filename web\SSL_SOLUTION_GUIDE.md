# Web版SSL证书验证问题解决方案

## 问题描述

您遇到的错误：
```
验证失败
连接测试失败: ClientException: Failed to fetch, uri=https://api.openai-proxy.org
```

这是Web版本特有的问题，由于浏览器的安全限制，无法直接跳过SSL证书验证。

## 解决方案：CORS代理服务器

我们提供了一个专门的CORS代理服务器来解决这个问题。

### 步骤1：在宝塔面板服务器上部署代理服务器

1. **上传文件到服务器**
   ```bash
   # 进入网站根目录
   cd /www/wwwroot/www.dznovel.top
   
   # 创建代理目录
   mkdir -p proxy
   cd proxy
   
   # 上传以下文件：
   # - cors_proxy_server.py
   # - start_proxy.py
   # - deploy_to_bt.sh
   ```

2. **运行部署脚本**
   ```bash
   chmod +x deploy_to_bt.sh
   ./deploy_to_bt.sh
   ```

3. **手动部署（如果脚本失败）**
   ```bash
   # 启动代理服务器
   python3 cors_proxy_server.py 8080
   
   # 或者使用启动脚本
   python3 start_proxy.py 8080
   ```

### 步骤2：配置Nginx反向代理（推荐）

在宝塔面板中，为您的网站添加以下Nginx配置：

```nginx
# 在网站配置文件中添加
location /proxy/ {
    proxy_pass http://127.0.0.1:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # CORS头部
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
    
    # 处理预检请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        add_header Access-Control-Max-Age 86400;
        add_header Content-Length 0;
        add_header Content-Type text/plain;
        return 204;
    }
}
```

### 步骤3：测试代理服务器

```bash
# 测试代理是否工作
curl "http://localhost:8080/proxy/https://httpbin.org/get"

# 测试通过域名访问
curl "https://www.dznovel.top/proxy/https://httpbin.org/get"
```

### 步骤4：配置防火墙

确保8080端口在防火墙中开放：

```bash
# 宝塔面板中开放8080端口
# 或者使用命令行
firewall-cmd --zone=public --add-port=8080/tcp --permanent
firewall-cmd --reload
```

## 代理服务器管理

### 使用systemd管理（推荐）

部署脚本会自动创建systemd服务，您可以使用以下命令管理：

```bash
# 启动服务
systemctl start dznovel-proxy

# 停止服务
systemctl stop dznovel-proxy

# 重启服务
systemctl restart dznovel-proxy

# 查看状态
systemctl status dznovel-proxy

# 查看日志
journalctl -u dznovel-proxy -f
```

### 手动管理

```bash
# 启动代理服务器
cd /www/wwwroot/www.dznovel.top/proxy
python3 cors_proxy_server.py 8080

# 后台运行
nohup python3 cors_proxy_server.py 8080 > proxy.log 2>&1 &
```

## 应用配置

代理服务器部署完成后，Web应用会自动检测并使用代理。

### 自动配置

应用会根据当前域名自动配置代理地址：
- 生产环境：`https://www.dznovel.top`（通过Nginx代理）
- 开发环境：`http://localhost:8081`

### 手动配置

如果需要手动配置代理地址，可以在应用启动时设置：

```dart
// 在main.dart中
WebConfig.initialize(proxyUrl: 'https://your-domain.com:8080');
```

## 故障排除

### 1. 代理服务器无法启动

**错误**: `Address already in use`
**解决**: 端口被占用，更换端口或停止占用进程

```bash
# 查看端口占用
netstat -tlnp | grep 8080

# 杀死占用进程
kill -9 <PID>

# 使用其他端口
python3 cors_proxy_server.py 8081
```

### 2. 仍然出现SSL错误

**检查项**:
- 代理服务器是否正常运行
- 防火墙是否开放端口
- Nginx配置是否正确

**测试步骤**:
```bash
# 1. 测试代理服务器
curl "http://localhost:8080/proxy/https://httpbin.org/get"

# 2. 测试通过域名访问
curl "https://www.dznovel.top/proxy/https://api.openai-proxy.org"

# 3. 检查服务状态
systemctl status dznovel-proxy
```

### 3. CORS错误

**可能原因**:
- Nginx配置不正确
- 代理服务器未正确添加CORS头部

**解决方法**:
- 检查Nginx配置中的CORS头部设置
- 重启Nginx和代理服务器
- 清除浏览器缓存

### 4. 连接超时

**可能原因**:
- 网络连接问题
- 目标API服务器响应慢

**解决方法**:
- 增加超时时间
- 检查网络连接
- 尝试其他API服务器

## 安全注意事项

1. **生产环境安全**:
   - 限制代理访问的域名范围
   - 使用HTTPS加密传输
   - 定期更新服务器

2. **API密钥安全**:
   - 不要在客户端暴露API密钥
   - 使用环境变量存储敏感信息

3. **访问控制**:
   - 限制代理服务器的访问IP
   - 使用防火墙规则

## 技术原理

1. **问题根源**: 浏览器的同源策略和SSL证书验证机制
2. **解决原理**: 代理服务器作为中介，处理SSL验证并添加CORS头部
3. **数据流**: 浏览器 → 代理服务器 → 目标API → 代理服务器 → 浏览器

这个解决方案确保Web版应用能够正常访问所有API服务，同时保持安全性和稳定性。

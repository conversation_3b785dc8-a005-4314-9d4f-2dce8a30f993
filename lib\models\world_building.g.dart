// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'world_building.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class WorldBuildingAdapter extends TypeAdapter<WorldBuilding> {
  @override
  final int typeId = 15;

  @override
  WorldBuilding read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return WorldBuilding(
      id: fields[0] as String?,
      novelId: fields[1] as String,
      timeBackground: fields[2] as String,
      geographicalSetting: fields[3] as String,
      socialStructure: fields[4] as String,
      culturalBackground: fields[5] as String,
      powerSystem: fields[6] as String?,
      economicSystem: fields[7] as String?,
      politicalSystem: fields[8] as String?,
      technologyLevel: fields[9] as String?,
      specialItems: fields[10] as String?,
      importantLocations: fields[11] as String?,
      historicalEvents: fields[12] as String?,
      myths: fields[13] as String?,
      createdAt: fields[14] as DateTime?,
      updatedAt: fields[15] as DateTime?,
      complexity: fields[16] as WorldBuildingComplexity,
    );
  }

  @override
  void write(BinaryWriter writer, WorldBuilding obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.novelId)
      ..writeByte(2)
      ..write(obj.timeBackground)
      ..writeByte(3)
      ..write(obj.geographicalSetting)
      ..writeByte(4)
      ..write(obj.socialStructure)
      ..writeByte(5)
      ..write(obj.culturalBackground)
      ..writeByte(6)
      ..write(obj.powerSystem)
      ..writeByte(7)
      ..write(obj.economicSystem)
      ..writeByte(8)
      ..write(obj.politicalSystem)
      ..writeByte(9)
      ..write(obj.technologyLevel)
      ..writeByte(10)
      ..write(obj.specialItems)
      ..writeByte(11)
      ..write(obj.importantLocations)
      ..writeByte(12)
      ..write(obj.historicalEvents)
      ..writeByte(13)
      ..write(obj.myths)
      ..writeByte(14)
      ..write(obj.createdAt)
      ..writeByte(15)
      ..write(obj.updatedAt)
      ..writeByte(16)
      ..write(obj.complexity);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorldBuildingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class WorldBuildingComplexityAdapter
    extends TypeAdapter<WorldBuildingComplexity> {
  @override
  final int typeId = 16;

  @override
  WorldBuildingComplexity read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return WorldBuildingComplexity.simple;
      case 1:
        return WorldBuildingComplexity.moderate;
      case 2:
        return WorldBuildingComplexity.complex;
      default:
        return WorldBuildingComplexity.simple;
    }
  }

  @override
  void write(BinaryWriter writer, WorldBuildingComplexity obj) {
    switch (obj) {
      case WorldBuildingComplexity.simple:
        writer.writeByte(0);
        break;
      case WorldBuildingComplexity.moderate:
        writer.writeByte(1);
        break;
      case WorldBuildingComplexity.complex:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorldBuildingComplexityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

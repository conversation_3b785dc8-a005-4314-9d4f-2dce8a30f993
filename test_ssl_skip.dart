import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

/// 测试SSL证书跳过功能
void main() async {
  print('开始测试SSL证书跳过功能...');
  
  // 测试目标URL
  final testUrls = [
    'https://api.openai-proxy.org',
    'https://generativelanguage.googleapis.com',
    'https://api.openai.com',
  ];
  
  for (final url in testUrls) {
    print('\n=== 测试 $url ===');
    await testSSLSkip(url);
  }
}

/// 测试SSL证书跳过
Future<void> testSSLSkip(String url) async {
  try {
    // 创建HttpClient并完全跳过SSL证书验证
    final httpClient = HttpClient();
    
    // 完全跳过SSL证书验证
    httpClient.badCertificateCallback = (cert, host, port) {
      print('跳过SSL证书验证: $host:$port');
      print('证书主题: ${cert.subject}');
      print('证书颁发者: ${cert.issuer}');
      print('证书有效期: ${cert.startValidity} - ${cert.endValidity}');
      return true; // 对所有证书都返回true
    };
    
    // 设置超时
    httpClient.connectionTimeout = const Duration(seconds: 10);
    httpClient.idleTimeout = const Duration(seconds: 30);
    
    final client = IOClient(httpClient);
    
    print('发送GET请求到: $url');
    final startTime = DateTime.now();
    
    final response = await client
        .get(Uri.parse(url))
        .timeout(const Duration(seconds: 30));
    
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    print('✅ 请求成功!');
    print('状态码: ${response.statusCode}');
    print('响应时间: ${duration.inMilliseconds}ms');
    print('响应头: ${response.headers}');
    
    // 如果响应体不太大，显示前200个字符
    if (response.body.length <= 200) {
      print('响应体: ${response.body}');
    } else {
      print('响应体(前200字符): ${response.body.substring(0, 200)}...');
    }
    
    client.close();
    
  } catch (e) {
    print('❌ 请求失败: $e');
    
    // 分析错误类型
    final errorStr = e.toString();
    if (errorStr.contains('CERTIFICATE_VERIFY_FAILED')) {
      print('⚠️  SSL证书验证仍然失败，可能需要检查代码配置');
    } else if (errorStr.contains('timeout')) {
      print('⚠️  请求超时，可能是网络问题');
    } else if (errorStr.contains('Connection refused')) {
      print('⚠️  连接被拒绝，可能是服务器问题');
    }
  }
}

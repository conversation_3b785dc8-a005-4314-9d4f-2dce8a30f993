# 岱宗文脉 Web版 SSL证书问题解决方案

## 问题描述

Web版应用在连接 `https://api.openai-proxy.org` 时出现SSL证书验证失败：

```
验证失败
连接测试失败: ClientException: Failed to fetch, uri=https://api.openai-proxy.org
```

## 解决方案概述

由于浏览器安全限制，Web版本无法直接跳过SSL证书验证。我们提供了一个CORS代理服务器来解决这个问题。

## 快速部署

### 方法1：一键部署（推荐）

```bash
# 1. 上传文件到服务器
cd /www/wwwroot/www.dznovel.top
mkdir -p proxy
cd proxy

# 2. 上传以下文件：
# - cors_proxy_server.py
# - quick_deploy.py
# - test_proxy.py

# 3. 运行一键部署
python3 quick_deploy.py
```

### 方法2：手动部署

```bash
# 1. 启动代理服务器
python3 cors_proxy_server.py 8080

# 2. 测试代理服务器
python3 test_proxy.py http://localhost:8080

# 3. 配置Nginx（可选）
# 在宝塔面板中添加location /proxy/配置
```

## 文件说明

| 文件名 | 描述 |
|--------|------|
| `cors_proxy_server.py` | CORS代理服务器主程序 |
| `quick_deploy.py` | 一键部署脚本 |
| `test_proxy.py` | 代理服务器测试工具 |
| `deploy_to_bt.sh` | 宝塔面板部署脚本 |
| `SSL_SOLUTION_GUIDE.md` | 详细解决方案指南 |

## 使用方法

### 1. 部署代理服务器

选择以下任一方法：

**方法A：使用Python脚本（推荐）**
```bash
python3 quick_deploy.py
```

**方法B：使用Shell脚本**
```bash
chmod +x deploy_to_bt.sh
./deploy_to_bt.sh
```

**方法C：手动启动**
```bash
python3 cors_proxy_server.py 8080
```

### 2. 测试代理服务器

```bash
# 快速测试
python3 test_proxy.py

# 完整测试
python3 test_proxy.py http://localhost:8080
```

### 3. 配置Nginx（可选但推荐）

在宝塔面板的网站配置中添加：

```nginx
location /proxy/ {
    proxy_pass http://127.0.0.1:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
    
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        add_header Access-Control-Max-Age 86400;
        add_header Content-Length 0;
        add_header Content-Type text/plain;
        return 204;
    }
}
```

## 服务管理

### 使用systemd（推荐）

```bash
# 启动服务
systemctl start dznovel-proxy

# 停止服务
systemctl stop dznovel-proxy

# 重启服务
systemctl restart dznovel-proxy

# 查看状态
systemctl status dznovel-proxy

# 查看日志
journalctl -u dznovel-proxy -f
```

### 手动管理

```bash
# 后台启动
nohup python3 cors_proxy_server.py 8080 > proxy.log 2>&1 &

# 查看进程
ps aux | grep cors_proxy_server

# 停止进程
pkill -f cors_proxy_server
```

## 测试验证

### 1. 基础测试

```bash
curl "http://localhost:8080/proxy/https://httpbin.org/get"
```

### 2. API测试

```bash
curl "http://localhost:8080/proxy/https://api.openai-proxy.org/v1/models"
```

### 3. 通过域名测试

```bash
curl "https://www.dznovel.top/proxy/https://httpbin.org/get"
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   netstat -tlnp | grep 8080
   kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   sudo python3 cors_proxy_server.py 8080
   ```

3. **防火墙问题**
   ```bash
   firewall-cmd --zone=public --add-port=8080/tcp --permanent
   firewall-cmd --reload
   ```

### 日志查看

```bash
# 系统服务日志
journalctl -u dznovel-proxy -f

# 手动启动日志
tail -f proxy.log
```

## 技术原理

1. **问题根源**: 浏览器CORS策略和SSL证书验证
2. **解决方案**: 代理服务器转发请求并处理SSL验证
3. **数据流**: 浏览器 → 代理服务器 → API服务器 → 代理服务器 → 浏览器

## 安全注意事项

- 仅在受信任的服务器上部署
- 定期更新代理服务器
- 使用HTTPS加密传输
- 限制代理访问范围

## 支持

如果遇到问题，请：

1. 查看详细指南：`SSL_SOLUTION_GUIDE.md`
2. 运行测试工具：`python3 test_proxy.py`
3. 检查服务日志：`journalctl -u dznovel-proxy -f`

---

**部署完成后，Web应用将自动使用代理服务器，无需额外配置。**

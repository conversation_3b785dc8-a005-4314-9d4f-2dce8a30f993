# API Key 隐藏功能实现说明

## 功能概述
为设置页面的所有API key输入框添加了隐藏/显示切换按钮，让用户可以自主选择是否隐藏API key内容，提高安全性和用户体验。

## 实现的功能点

### 1. 主设置页面的模型API Key输入框
- **位置**: `lib/screens/settings_screen.dart` (第1242行)
- **功能**: 添加了眼睛图标按钮，可以切换API key的显示/隐藏状态
- **状态管理**: 通过 `ApiConfigController.isApiKeyVisible` 控制

### 2. 嵌入模型的API Key输入框
- **位置**: `lib/screens/settings_screen.dart` (第2351行)
- **功能**: 为嵌入模型API key添加独立的显示/隐藏控制
- **状态管理**: 通过 `ApiConfigController.isEmbeddingApiKeyVisible` 控制

### 3. TTS API Key输入框
- **位置**: `lib/screens/tts/tts_screen.dart` (第407行)
- **功能**: 为文本转语音API key添加显示/隐藏控制
- **状态管理**: 通过 `ApiConfigController.isTtsApiKeyVisible` 控制

### 4. 网络测试页面的API Key输入框
- **位置**: `lib/screens/network_test_screen.dart` (第129行)
- **功能**: 为Google API密钥测试输入框添加显示/隐藏控制
- **状态管理**: 使用本地状态 `_isApiKeyVisible`

### 5. API配置页面的API Key输入框
- **位置**: `lib/screens/settings/api_config_screen.dart` (第43行)
- **功能**: 为DeepSeek API Key添加显示/隐藏控制
- **状态管理**: 使用本地状态 `_isApiKeyVisible`

## 技术实现细节

### 控制器层面的改动 (`ApiConfigController`)

1. **添加响应式状态变量**:
   ```dart
   final isApiKeyVisible = false.obs;
   final isEmbeddingApiKeyVisible = false.obs;
   final isTtsApiKeyVisible = false.obs;
   ```

2. **添加存储键**:
   ```dart
   static const String _apiKeyVisibleKey = 'api_key_visible';
   static const String _embeddingApiKeyVisibleKey = 'embedding_api_key_visible';
   static const String _ttsApiKeyVisibleKey = 'tts_api_key_visible';
   ```

3. **添加切换方法**:
   ```dart
   void toggleApiKeyVisibility();
   void toggleEmbeddingApiKeyVisibility();
   void toggleTtsApiKeyVisibility();
   ```

4. **持久化存储**: 状态会自动保存到本地存储，应用重启后保持用户的选择

### UI层面的改动

1. **TextField属性修改**:
   - 添加 `obscureText` 属性绑定到可见性状态
   - 在 `InputDecoration` 中添加 `suffixIcon`

2. **图标按钮实现**:
   ```dart
   suffixIcon: IconButton(
     icon: Icon(
       controller.isApiKeyVisible.value
           ? Icons.visibility
           : Icons.visibility_off,
     ),
     onPressed: controller.toggleApiKeyVisibility,
   ),
   ```

3. **响应式更新**: 使用 `Obx()` 包装TextField确保状态变化时UI自动更新

## 用户体验改进

1. **安全性提升**: 默认隐藏API key，防止他人偷窥
2. **灵活性**: 用户可以根据需要随时切换显示状态
3. **一致性**: 所有API key输入框都有统一的交互方式
4. **持久化**: 用户的显示偏好会被记住，无需重复设置
5. **直观性**: 使用标准的眼睛图标，符合用户习惯

## 使用方法

1. 打开任何包含API key输入框的页面
2. 在API key输入框右侧会看到眼睛图标按钮
3. 点击按钮可以在显示/隐藏之间切换
4. 设置会自动保存，下次打开应用时保持上次的选择

## 兼容性说明

- 所有修改都向后兼容，不影响现有功能
- 默认状态为隐藏，提供更好的安全性
- 适用于所有平台（Android、iOS、Web、Desktop）

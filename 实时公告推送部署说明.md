# 🚀 实时公告推送系统部署说明

## 📋 系统概述

您的应用现在已配置了实时公告推送系统，具有以下特点：

- ⚡ **实时推送**：每30秒自动检查更新
- 📝 **简单配置**：只需编辑JSON文件
- 🗄️ **无需数据库**：直接文件读取
- 🔄 **即时生效**：修改后立即推送
- 🌐 **双域名支持**：主域名+备用域名容错

## 🔧 服务器配置

### 1. 上传公告文件

将 `announcement.json` 文件上传到您的服务器，确保以下URL可以访问：

**主域名**：`https://www.dznovel.top/announcement.json`
**备用域名**：`http://47.120.19.139:8000/announcement.json`

### 2. 文件权限设置

确保Web服务器可以读取该文件：
```bash
chmod 644 announcement.json
```

### 3. CORS配置（如需要）

如果遇到跨域问题，请在Web服务器配置中添加CORS头：
```nginx
location /announcement.json {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods GET;
}
```

## 📝 使用方法

### 发布新公告

1. **编辑公告文件**：修改服务器上的 `announcement.json`
2. **更改ID**：修改 `id` 字段确保唯一性（这是触发新公告的关键）
3. **保存文件**：保存到服务器
4. **等待推送**：用户设备将在30秒内收到推送

### JSON字段说明

```json
{
  "id": "unique_announcement_id",           // 唯一标识，修改此字段触发新公告
  "title": "公告标题",                      // 公告标题
  "content": "公告内容\\n支持换行",          // 公告内容，\\n表示换行
  "created_at": "2025-01-27T12:00:00Z",    // 创建时间（ISO格式）
  "is_important": true,                     // 是否重要公告（影响显示样式）
  "is_active": true                         // 是否激活（false时不显示）
}
```

## 🎯 实际操作示例

### 示例1：发布普通公告
```json
{
  "id": "notice_20250127_001",
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-24:00进行维护，期间可能影响使用。\\n感谢您的理解与支持！",
  "created_at": "2025-01-27T14:00:00Z",
  "is_important": false,
  "is_active": true
}
```

### 示例2：发布重要公告
```json
{
  "id": "urgent_20250127_002",
  "title": "⚠️ 重要安全更新",
  "content": "发现安全漏洞，请立即更新到最新版本！\\n\\n更新内容：\\n• 修复登录安全问题\\n• 优化数据加密\\n• 提升系统稳定性",
  "created_at": "2025-01-27T15:30:00Z",
  "is_important": true,
  "is_active": true
}
```

### 示例3：停用公告
```json
{
  "id": "disabled_announcement",
  "title": "已停用的公告",
  "content": "此公告已停用",
  "created_at": "2025-01-27T16:00:00Z",
  "is_important": false,
  "is_active": false
}
```

## 🔍 测试方法

1. **修改ID测试**：将现有公告的 `id` 改为新值，保存文件
2. **观察日志**：查看应用控制台，应该看到检查公告的日志
3. **等待推送**：最多等待30秒，新公告应该自动弹出

## 📊 系统监控

应用会在控制台输出以下日志：
- `🔄 启动实时公告检查定时器`
- `⏰ 定时检查公告更新...`
- `🆕 发现新公告: [标题]`
- `📋 公告无更新`

## ⚠️ 注意事项

1. **ID唯一性**：每次发布新公告必须使用不同的ID
2. **文件格式**：确保JSON格式正确，建议使用JSON验证工具
3. **网络访问**：确保应用能访问公告文件URL
4. **缓存问题**：某些CDN可能有缓存，注意清除缓存
5. **频率控制**：30秒检查间隔已经很频繁，不建议调整更短

## 🛠️ 故障排除

### 公告不显示
1. 检查JSON格式是否正确
2. 确认 `is_active` 为 `true`
3. 验证URL是否可访问
4. 查看应用控制台日志

### 推送延迟
1. 检查网络连接
2. 确认服务器响应正常
3. 查看是否有缓存影响

### 重复推送
1. 确认每次都修改了ID
2. 检查是否有多个应用实例运行

## 🎉 完成！

您的实时公告推送系统现在已经完全配置好了！只需要在服务器上编辑一个简单的JSON文件，就能向所有用户实时推送公告。

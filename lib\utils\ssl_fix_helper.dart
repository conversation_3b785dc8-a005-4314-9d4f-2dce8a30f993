import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:flutter/foundation.dart';

/// SSL证书问题修复助手
/// 专门用于解决SSL握手失败和证书验证问题
class SSLFixHelper {
  /// 创建一个忽略SSL证书验证的HTTP客户端
  /// 主要用于解决代理环境下的证书验证问题
  static http.Client createTrustAllClient({
    Duration? timeout,
    String? proxyUrl,
  }) {
    if (kIsWeb) {
      return http.Client();
    }

    final httpClient = HttpClient();

    // 设置超时
    httpClient.connectionTimeout = timeout ?? const Duration(seconds: 30);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 120);

    // 设置代理（如果提供）
    if (proxyUrl != null && proxyUrl.isNotEmpty) {
      httpClient.findProxy = (uri) => 'PROXY $proxyUrl';
      print('SSL修复客户端使用代理: $proxyUrl');
    }

    // 启用自动解压缩
    httpClient.autoUncompress = true;

    // 设置用户代理
    httpClient.userAgent = 'DaiZongAI/1.0 (SSL-Fix-Mode)';

    // 完全信任所有证书（仅用于解决SSL问题）
    httpClient.badCertificateCallback = (cert, host, port) {
      print('SSL修复模式 - 跳过证书验证: $host:$port');
      print('证书主题: ${cert.subject}');
      print('证书颁发者: ${cert.issuer}');
      print('证书有效期: ${cert.startValidity} - ${cert.endValidity}');
      
      // 在修复模式下，信任所有证书
      return true;
    };

    return IOClient(httpClient);
  }

  /// 创建一个跳过所有SSL证书验证的HTTP客户端
  /// 现在默认跳过所有证书验证以解决SSL问题
  static http.Client createTrustedServicesClient({
    Duration? timeout,
    String? proxyUrl,
  }) {
    if (kIsWeb) {
      return http.Client();
    }

    final httpClient = HttpClient();

    // 设置超时
    httpClient.connectionTimeout = timeout ?? const Duration(seconds: 30);
    httpClient.idleTimeout = timeout ?? const Duration(seconds: 120);

    // 设置代理（如果提供）
    if (proxyUrl != null && proxyUrl.isNotEmpty) {
      httpClient.findProxy = (uri) => 'PROXY $proxyUrl';
      print('可信服务客户端使用代理: $proxyUrl');
    }

    // 启用自动解压缩
    httpClient.autoUncompress = true;

    // 设置用户代理
    httpClient.userAgent = 'DaiZongAI/1.0 (SSL-Skip-Mode)';

    // 完全跳过SSL证书验证
    httpClient.badCertificateCallback = (cert, host, port) {
      print('可信服务客户端 - 跳过SSL证书验证: $host:$port');
      print('证书主题: ${cert.subject}');
      print('证书颁发者: ${cert.issuer}');
      // 对所有主机都返回true，完全跳过证书验证
      return true;
    };

    return IOClient(httpClient);
  }

  /// 测试SSL连接
  static Future<Map<String, dynamic>> testSSLConnection({
    required String url,
    Duration? timeout,
    String? proxyUrl,
    bool useTrustAllMode = false,
  }) async {
    try {
      print('开始测试SSL连接: $url');
      print('使用信任所有证书模式: $useTrustAllMode');

      final client = useTrustAllMode 
          ? createTrustAllClient(timeout: timeout, proxyUrl: proxyUrl)
          : createTrustedServicesClient(timeout: timeout, proxyUrl: proxyUrl);

      final startTime = DateTime.now();
      final response = await client
          .get(Uri.parse(url))
          .timeout(timeout ?? const Duration(seconds: 30));
      final endTime = DateTime.now();

      client.close();

      return {
        'success': true,
        'statusCode': response.statusCode,
        'duration': endTime.difference(startTime).inMilliseconds,
        'message': 'SSL连接成功',
        'headers': response.headers,
      };
    } catch (e) {
      print('SSL连接测试失败: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'SSL连接失败: $e',
      };
    }
  }

  /// 获取SSL修复建议
  static Map<String, dynamic> getSSLFixSuggestions(String errorMessage) {
    final suggestions = <String>[];
    
    if (errorMessage.contains('CERTIFICATE_VERIFY_FAILED')) {
      suggestions.addAll([
        '1. 检查系统时间是否正确',
        '2. 更新系统根证书',
        '3. 检查代理服务器的SSL配置',
        '4. 尝试使用信任所有证书模式（仅用于测试）',
      ]);
    }
    
    if (errorMessage.contains('unable to get local issuer certificate')) {
      suggestions.addAll([
        '1. 安装缺失的根证书',
        '2. 检查证书链是否完整',
        '3. 验证代理服务器证书配置',
      ]);
    }
    
    if (errorMessage.contains('HandshakeException')) {
      suggestions.addAll([
        '1. 检查TLS版本兼容性',
        '2. 验证加密套件支持',
        '3. 检查防火墙和网络配置',
      ]);
    }

    return {
      'error': errorMessage,
      'suggestions': suggestions,
      'canUseTrustAllMode': true,
      'recommendedAction': '建议先尝试使用可信服务模式，如果仍然失败，可以临时使用信任所有证书模式进行测试',
    };
  }
}

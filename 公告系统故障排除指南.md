# 🔧 公告系统故障排除指南

## 🚨 问题：服务器有公告但显示"无公告"

### 📋 排查步骤

#### 1. 检查网络连接
```bash
# 运行网络测试工具
dart test_announcement_network.dart
```

#### 2. 手动验证URL
在浏览器中访问以下URL，确认能看到JSON内容：
- `https://www.dznovel.top/announcement.json`
- `http://47.120.19.139:8000/announcement.json`

#### 3. 检查JSON格式
确保JSON文件格式正确：
```json
{
  "id": "your_announcement_id",
  "title": "公告标题",
  "content": "公告内容",
  "created_at": "2025-01-27T12:00:00Z",
  "is_active": true,
  "is_important": false
}
```

#### 4. 查看应用调试日志
启动应用后，在控制台查找以下日志：

**正常情况：**
```
🔔 初始化公告服务...
🔄 启动实时公告检查定时器，每30秒检查一次
⏰ 定时检查公告更新...
🌐 开始获取公告...
📡 主域名URL: https://www.dznovel.top/announcement.json
🔄 尝试主域名: https://www.dznovel.top/announcement.json
📊 主域名响应状态: 200
✅ 主域名响应成功，解析JSON...
🔍 is_active 值: true
✅ 公告已激活，创建Announcement对象
🎯 成功创建公告: 您的公告标题
```

**异常情况：**
```
❌ 主域名获取公告失败，尝试备用地址: [错误信息]
❌ 备用域名也失败: [错误信息]
❌ 所有域名都无法获取有效公告
```

### 🔍 常见问题及解决方案

#### 问题1: 网络连接失败
**症状：** 看到 "网络请求失败" 或 "SocketException"
**解决方案：**
1. 检查网络连接
2. 确认服务器运行状态
3. 检查防火墙设置
4. 验证URL是否正确

#### 问题2: JSON格式错误
**症状：** 看到 "JSON解析失败"
**解决方案：**
1. 使用JSON验证工具检查格式
2. 确保所有字符串用双引号
3. 检查是否有多余的逗号
4. 确保UTF-8编码

#### 问题3: 公告未激活
**症状：** 看到 "公告未激活 (is_active = false)"
**解决方案：**
1. 将 `is_active` 设置为 `true`
2. 保存并重新上传文件

#### 问题4: CORS跨域问题
**症状：** 浏览器控制台显示CORS错误
**解决方案：**
在服务器配置中添加CORS头：
```nginx
location /announcement.json {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods GET;
    add_header Access-Control-Allow-Headers *;
}
```

#### 问题5: 缓存问题
**症状：** 修改文件后不生效
**解决方案：**
1. 清除浏览器缓存
2. 在URL后添加时间戳：`announcement.json?t=123456`
3. 设置正确的缓存头：
```nginx
location /announcement.json {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}
```

#### 问题6: 文件权限问题
**症状：** 403 Forbidden 错误
**解决方案：**
```bash
chmod 644 announcement.json
chown www-data:www-data announcement.json
```

### 🧪 测试方法

#### 方法1: 使用网络测试工具
```bash
dart test_announcement_network.dart
```

#### 方法2: 使用curl命令
```bash
curl -v "https://www.dznovel.top/announcement.json"
curl -v "http://47.120.19.139:8000/announcement.json"
```

#### 方法3: 浏览器开发者工具
1. 打开浏览器开发者工具
2. 访问公告URL
3. 查看Network标签页
4. 检查响应状态和内容

### 📊 调试技巧

#### 1. 启用详细日志
应用已经添加了详细的调试日志，启动应用后观察控制台输出。

#### 2. 临时修改检查间隔
如果需要更频繁的测试，可以临时修改检查间隔：
```dart
// 在 lib/services/announcement_service.dart 中
static const int _checkIntervalSeconds = 5; // 改为5秒测试
```

#### 3. 清除应用缓存
```dart
// 在应用中调用
final announcementService = Get.find<AnnouncementService>();
await announcementService.clearCache();
```

### 🎯 验证清单

- [ ] 服务器URL可以在浏览器中访问
- [ ] JSON格式正确且有效
- [ ] `is_active` 字段为 `true`
- [ ] 网络连接正常
- [ ] 没有CORS错误
- [ ] 文件权限正确
- [ ] 应用日志显示正常

### 📞 获取帮助

如果问题仍然存在，请提供以下信息：
1. 完整的应用调试日志
2. 网络测试工具的输出
3. 浏览器访问URL的结果
4. 服务器配置信息
5. announcement.json文件内容

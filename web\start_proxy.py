#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORS代理服务器启动脚本
用于在宝塔面板服务器上快速启动代理服务器
"""

import os
import sys
import subprocess
import socket
import time
from pathlib import Path

def find_free_port(start_port=8080):
    """查找可用端口"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def check_port_in_use(port):
    """检查端口是否被占用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', port))
            return False
    except OSError:
        return True

def start_proxy_server(port=8080):
    """启动代理服务器"""
    script_dir = Path(__file__).parent
    proxy_script = script_dir / 'cors_proxy_server.py'
    
    if not proxy_script.exists():
        print(f"❌ 找不到代理服务器脚本: {proxy_script}")
        return False
    
    # 检查端口是否可用
    if check_port_in_use(port):
        print(f"⚠️  端口 {port} 已被占用，正在查找可用端口...")
        port = find_free_port(port)
        if not port:
            print("❌ 无法找到可用端口")
            return False
        print(f"✅ 找到可用端口: {port}")
    
    try:
        print(f"🚀 启动CORS代理服务器...")
        print(f"📡 端口: {port}")
        print(f"🌐 访问地址: http://localhost:{port}")
        print(f"🔧 代理格式: http://localhost:{port}/proxy/https://api.openai-proxy.org")
        print("-" * 60)
        
        # 启动代理服务器
        subprocess.run([sys.executable, str(proxy_script), str(port)])
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 岱宗文脉 CORS代理服务器启动器")
    print("=" * 50)
    
    # 获取端口参数
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"❌ 无效的端口号: {sys.argv[1]}")
            print("使用方法: python start_proxy.py [port]")
            return 1
    
    # 启动代理服务器
    success = start_proxy_server(port)
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

/// 测试Gemini API代理连接的独立脚本
void main() async {
  print('开始测试Gemini API代理连接...');
  
  // 测试不同的代理配置
  await testDirectConnection();
  await testProxyConnection('127.0.0.1:7890');
  await testProxyConnection('127.0.0.1:7891');
  await testSmartProxyConnection();
}

/// 测试直连
Future<void> testDirectConnection() async {
  print('\n=== 测试直连 ===');
  try {
    final client = http.Client();
    await testGeminiCall(client, '直连');
  } catch (e) {
    print('直连失败: $e');
  }
}

/// 测试指定代理连接
Future<void> testProxyConnection(String proxyUrl) async {
  print('\n=== 测试代理连接: $proxyUrl ===');
  try {
    final httpClient = HttpClient();
    httpClient.findProxy = (_) => 'PROXY $proxyUrl';
    httpClient.connectionTimeout = const Duration(seconds: 10);
    
    final client = IOClient(httpClient);
    await testGeminiCall(client, '代理($proxyUrl)');
  } catch (e) {
    print('代理连接失败: $e');
  }
}

/// 测试智能代理连接
Future<void> testSmartProxyConnection() async {
  print('\n=== 测试智能代理连接 ===');
  try {
    final httpClient = HttpClient();
    
    // 模拟智能代理逻辑
    httpClient.findProxy = (uri) {
      if (uri.host.contains('google') || uri.host.contains('googleapis.com')) {
        print('智能代理：检测到Google服务，使用代理链');
        return 'PROXY 127.0.0.1:7890; PROXY 127.0.0.1:7891; DIRECT';
      }
      return 'DIRECT';
    };
    
    httpClient.connectionTimeout = const Duration(seconds: 15);
    httpClient.badCertificateCallback = (cert, host, port) {
      return host.contains('googleapis.com');
    };
    
    final client = IOClient(httpClient);
    await testGeminiCall(client, '智能代理');
  } catch (e) {
    print('智能代理连接失败: $e');
  }
}

/// 执行Gemini API调用测试
Future<void> testGeminiCall(http.Client client, String method) async {
  const apiKey = 'YOUR_API_KEY_HERE'; // 请替换为您的实际API密钥
  
  if (apiKey == 'YOUR_API_KEY_HERE') {
    print('请在代码中设置您的Gemini API密钥');
    return;
  }
  
  try {
    final url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$apiKey';
    
    final requestBody = {
      'contents': [
        {
          'parts': [
            {'text': '请简单回答"你好"'}
          ]
        }
      ],
      'generationConfig': {
        'maxOutputTokens': 50,
        'temperature': 0.1,
      }
    };
    
    print('发送请求到: $url');
    final startTime = DateTime.now();
    
    final response = await client
        .post(
          Uri.parse(url),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(requestBody),
        )
        .timeout(const Duration(seconds: 30));
    
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    print('$method - 响应状态: ${response.statusCode}');
    print('$method - 响应时间: ${duration.inMilliseconds}ms');
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['candidates'] != null && data['candidates'].isNotEmpty) {
        final text = data['candidates'][0]['content']['parts'][0]['text'];
        print('$method - 响应内容: $text');
        print('$method - ✅ 成功');
      } else {
        print('$method - ⚠️ 响应格式异常');
      }
    } else {
      print('$method - ❌ 失败: ${response.body}');
    }
  } catch (e) {
    print('$method - ❌ 异常: $e');
  } finally {
    client.close();
  }
}

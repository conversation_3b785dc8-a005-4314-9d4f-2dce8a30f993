#!/usr/bin/env python3
"""
简单的本地HTTP服务器，用于测试公告推送功能
运行方法：python start_local_server.py
然后访问：http://localhost:8000/announcement.json
"""

import http.server
import socketserver
import os
import json
from datetime import datetime

PORT = 8000

class AnnouncementHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头，允许跨域访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_GET(self):
        if self.path == '/announcement.json':
            # 确保announcement.json文件存在
            if not os.path.exists('announcement.json'):
                # 创建默认公告
                default_announcement = {
                    "id": f"local_test_{int(datetime.now().timestamp())}",
                    "title": "🧪 本地测试公告",
                    "content": "这是本地测试服务器的公告。\n\n如果您看到这个公告，说明本地测试服务器正常工作！\n\n要发布新公告，请编辑 announcement.json 文件。",
                    "created_at": datetime.now().isoformat() + "Z",
                    "is_important": False,
                    "is_active": True
                }
                
                with open('announcement.json', 'w', encoding='utf-8') as f:
                    json.dump(default_announcement, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 创建了默认公告文件")
        
        # 调用父类方法处理请求
        super().do_GET()

def main():
    print(f"🚀 启动本地公告测试服务器...")
    print(f"📡 服务器地址: http://localhost:{PORT}")
    print(f"📄 公告地址: http://localhost:{PORT}/announcement.json")
    print(f"🛑 按 Ctrl+C 停止服务器")
    print()
    
    # 确保在正确的目录中
    if not os.path.exists('announcement.json'):
        print("⚠️  未找到 announcement.json 文件，将自动创建...")
    
    with socketserver.TCPServer(("", PORT), AnnouncementHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

if __name__ == "__main__":
    main()

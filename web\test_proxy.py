#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
代理服务器测试脚本
用于测试CORS代理服务器是否正常工作
"""

import sys
import urllib.request
import urllib.error
import json
import time

def test_proxy_server(proxy_url, target_url):
    """测试代理服务器"""
    print(f"🧪 测试代理服务器")
    print(f"代理地址: {proxy_url}")
    print(f"目标地址: {target_url}")
    print("-" * 50)
    
    try:
        # 构建代理请求URL
        if not proxy_url.endswith('/'):
            proxy_url += '/'
        
        full_url = f"{proxy_url}proxy/{target_url}"
        print(f"完整请求URL: {full_url}")
        
        # 发送请求
        start_time = time.time()
        with urllib.request.urlopen(full_url, timeout=30) as response:
            end_time = time.time()
            
            # 读取响应
            response_data = response.read()
            response_time = (end_time - start_time) * 1000
            
            print(f"✅ 请求成功")
            print(f"状态码: {response.status}")
            print(f"响应时间: {response_time:.2f}ms")
            print(f"响应大小: {len(response_data)} bytes")
            
            # 打印响应头
            print("\n📋 响应头:")
            for header, value in response.headers.items():
                print(f"  {header}: {value}")
            
            # 尝试解析JSON响应
            try:
                if response_data:
                    json_data = json.loads(response_data.decode('utf-8'))
                    print(f"\n📄 响应内容 (JSON):")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
            except:
                print(f"\n📄 响应内容 (文本):")
                print(response_data.decode('utf-8', errors='ignore')[:500])
            
            return True
            
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误: {e.code} {e.reason}")
        try:
            error_data = e.read()
            print(f"错误响应: {error_data.decode('utf-8', errors='ignore')}")
        except:
            pass
        return False
        
    except urllib.error.URLError as e:
        print(f"❌ URL错误: {e.reason}")
        return False
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_common_apis(proxy_url):
    """测试常见API"""
    test_cases = [
        {
            'name': 'HTTPBin测试',
            'url': 'https://httpbin.org/get',
            'description': '基础HTTP测试'
        },
        {
            'name': 'OpenAI代理测试',
            'url': 'https://api.openai-proxy.org/v1/models',
            'description': '测试OpenAI代理连接'
        },
        {
            'name': 'Google API测试',
            'url': 'https://generativelanguage.googleapis.com/v1beta/models',
            'description': '测试Google API连接'
        }
    ]
    
    print("🎯 开始测试常见API")
    print("=" * 60)
    
    results = []
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"描述: {test_case['description']}")
        
        success = test_proxy_server(proxy_url, test_case['url'])
        results.append({
            'name': test_case['name'],
            'success': success
        })
        
        if i < len(test_cases):
            print("\n" + "="*60)
    
    # 打印测试结果摘要
    print("\n" + "="*60)
    print("📊 测试结果摘要")
    print("-" * 30)
    
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"  {result['name']}: {status}")
    
    print(f"\n总计: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！代理服务器工作正常")
    elif success_count > 0:
        print("⚠️  部分测试通过，请检查失败的API连接")
    else:
        print("❌ 所有测试失败，请检查代理服务器配置")
    
    return success_count == total_count

def main():
    """主函数"""
    print("🧪 CORS代理服务器测试工具")
    print("=" * 40)
    
    # 获取代理地址
    if len(sys.argv) > 1:
        proxy_url = sys.argv[1]
    else:
        proxy_url = input("请输入代理服务器地址 (默认: http://localhost:8080): ").strip()
        if not proxy_url:
            proxy_url = "http://localhost:8080"
    
    print(f"使用代理地址: {proxy_url}")
    
    # 选择测试模式
    print("\n选择测试模式:")
    print("1. 快速测试 (HTTPBin)")
    print("2. 完整测试 (所有API)")
    print("3. 自定义测试")
    
    choice = input("请选择 (1-3, 默认: 1): ").strip()
    
    if choice == '2':
        # 完整测试
        success = test_common_apis(proxy_url)
        return 0 if success else 1
        
    elif choice == '3':
        # 自定义测试
        target_url = input("请输入目标URL: ").strip()
        if not target_url:
            print("❌ 目标URL不能为空")
            return 1
        
        success = test_proxy_server(proxy_url, target_url)
        return 0 if success else 1
        
    else:
        # 快速测试
        success = test_proxy_server(proxy_url, "https://httpbin.org/get")
        return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())

import 'dart:io';
import 'dart:convert';

/// 简化的OpenAI代理连接诊断工具
void main() async {
  print('🔍 OpenAI代理连接诊断工具');
  print('=' * 50);

  // 常见的OpenAI代理服务器
  final testUrls = [
    'https://api.openai-proxy.org',
    'https://api.openai.com',
    'https://openai.api2d.net',
    'https://api.chatanywhere.com.cn',
  ];

  print('📡 检测系统代理状态...');
  await detectSystemProxy();
  print('');

  for (final url in testUrls) {
    print('🌐 测试连接: $url');
    print('-' * 30);

    await testConnection(url);
    print('');
  }

  print('🔧 故障排除建议:');
  print('1. 如果所有连接都失败，请检查网络连接');
  print('2. 如果出现"Connection closed before full header was received"错误:');
  print('   - 代理服务器可能不稳定');
  print('   - 尝试更换代理服务器');
  print('   - 检查防火墙设置');
  print('3. 如果连接超时:');
  print('   - 网络延迟可能较高');
  print('   - 尝试增加超时时间');
  print('   - 检查代理设置');
  print('4. 建议使用稳定的代理服务或VPN');

  print('\n🏁 诊断完成！');
}

/// 检测系统代理配置
Future<void> detectSystemProxy() async {
  final httpProxy = Platform.environment['HTTP_PROXY'] ??
      Platform.environment['http_proxy'];
  final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
      Platform.environment['https_proxy'];

  print('环境变量代理: ${httpsProxy ?? httpProxy ?? '未设置'}');

  // 检测常见代理端口
  final commonPorts = ['7890', '7891', '10809', '1080', '8080'];
  final availablePorts = <String>[];

  for (final port in commonPorts) {
    try {
      final socket = await Socket.connect('127.0.0.1', int.parse(port))
          .timeout(const Duration(seconds: 1));
      socket.destroy();
      availablePorts.add(port);
    } catch (e) {
      // 端口不可用
    }
  }

  print('可用代理端口: $availablePorts');
  print('推荐代理: ${availablePorts.isNotEmpty ? '127.0.0.1:${availablePorts.first}' : '无'}');
}

/// 测试连接
Future<void> testConnection(String url) async {
  HttpClient? client;

  try {
    client = HttpClient();
    client.connectionTimeout = const Duration(seconds: 15);
    client.idleTimeout = const Duration(seconds: 30);

    // 禁用SSL证书验证
    client.badCertificateCallback = (cert, host, port) => true;

    final testUrl = url.endsWith('/') ? '${url}v1/models' : '$url/v1/models';
    final uri = Uri.parse(testUrl);

    final startTime = DateTime.now();

    final request = await client.getUrl(uri);
    request.headers.set('User-Agent', 'DaiZongAI/1.0 (Connection Test)');

    final response = await request.close();

    final endTime = DateTime.now();
    final responseTime = endTime.difference(startTime).inMilliseconds;

    if (response.statusCode < 500) {
      print('✅ 连接成功！响应时间: ${responseTime}ms');
      print('📊 状态码: ${response.statusCode}');
    } else {
      print('⚠️ 连接成功但服务器错误，状态码: ${response.statusCode}，响应时间: ${responseTime}ms');
    }

  } catch (e) {
    String errorType = '未知错误';
    String suggestion = '';

    if (e.toString().contains('Connection closed before full header was received')) {
      errorType = '连接过早关闭';
      suggestion = '代理服务器可能不稳定，建议：\n   - 检查代理服务器状态\n   - 尝试其他代理服务器\n   - 检查网络连接';
    } else if (e.toString().contains('SocketException')) {
      errorType = '网络连接失败';
      suggestion = '无法连接到服务器，建议：\n   - 检查网络连接\n   - 检查防火墙设置\n   - 确认服务器地址正确';
    } else if (e.toString().contains('TimeoutException')) {
      errorType = '连接超时';
      suggestion = '连接超时，建议：\n   - 检查网络速度\n   - 尝试增加超时时间\n   - 检查服务器响应速度';
    }

    print('❌ 连接失败: $errorType');
    print('🔍 错误详情: $e');
    print('💡 建议: $suggestion');

  } finally {
    client?.close();
  }
}

import 'dart:io';
import 'package:http/http.dart' as http;
import 'lib/utils/network_client.dart';

/// 测试SSL修复是否有效
void main() async {
  print('🔧 开始测试SSL修复功能...\n');
  
  // 测试URL列表 - 包含一些可能有SSL问题的URL
  final testUrls = [
    'https://api.openai-proxy.org',
    'https://generativelanguage.googleapis.com',
    'https://api.openai.com',
    'https://www.google.com',
  ];
  
  for (int i = 0; i < testUrls.length; i++) {
    final url = testUrls[i];
    print('🌐 测试URL ${i + 1}: $url');
    
    // 测试1: 使用原始http.get（可能失败）
    print('📡 测试原始HTTP客户端...');
    try {
      final response = await http.get(Uri.parse(url))
          .timeout(Duration(seconds: 10));
      print('✅ 原始客户端成功 - 状态码: ${response.statusCode}');
    } catch (e) {
      print('❌ 原始客户端失败: $e');
    }
    
    // 测试2: 使用SSL修复的客户端
    print('🔧 测试SSL修复客户端...');
    final client = NetworkClient.createStableConnectionClient(
      timeout: Duration(seconds: 10),
    );
    
    try {
      final response = await client.get(Uri.parse(url))
          .timeout(Duration(seconds: 10));
      print('✅ SSL修复客户端成功 - 状态码: ${response.statusCode}');
      print('📏 响应长度: ${response.body.length} 字符');
    } catch (e) {
      print('❌ SSL修复客户端失败: $e');
    } finally {
      client.close();
    }
    
    print('\n' + '-' * 60 + '\n');
  }
  
  print('🎯 SSL修复测试完成！');
  print('💡 如果SSL修复客户端成功而原始客户端失败，说明修复有效。');
}
